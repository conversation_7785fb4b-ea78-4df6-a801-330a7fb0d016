import { useState } from 'react';

import useChatNotification from '@/shared/hooks/common/useChatNotification';

import ChatContent from './ChatContent';
import ChatHeader from './ChatHeader';
import ChatInput from './ChatInput';

// Legacy message type for backward compatibility
interface Message {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  timestamp: Date;
  avatar?: string;
}

interface ChatPanelProps {
  onClose: () => void;
  onKeywordDetected?: (keyword: string) => void;
}

const ChatPanel = ({ onClose, onKeywordDetected }: ChatPanelProps) => {
  const [messages, setMessages] = useState<Message[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { notifications, addNotification, removeNotification } = useChatNotification();

  // Handle new chat
  const handleNewChat = () => {
    setMessages([]);
  };

  // Handle send message
  const handleSendMessage = (content: string) => {
    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content,
      sender: 'user',
      timestamp: new Date(),
    };

    setMessages(prev => [...prev, userMessage]);
    setIsLoading(true);

    // Simulate AI response after a delay
    setTimeout(() => {
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: `This is a response to: "${content}"`,
        sender: 'ai',
        timestamp: new Date(),
        avatar: '/assets/images/ai-agents/assistant-robot.svg',
      };

      setMessages(prev => [...prev, aiMessage]);
      setIsLoading(false);
    }, 1000);
  };

  // Custom notification handler
  const showNotification = (type: 'success' | 'error' | 'warning' | 'info', message: string) => {
    console.log(`[ChatPanel] Showing notification: ${type} - ${message}`);

    // Thêm notification mới và tự động xóa sau 5 giây
    const id = addNotification(type, message, 5000);
    console.log(`[ChatPanel] Added notification with ID: ${id}`);

    // Kiểm tra xem notification đã được thêm vào state chưa
    console.log(`[ChatPanel] Current notifications:`, notifications);

    // Đảm bảo ChatContent được cập nhật
    setTimeout(() => {
      console.log(
        `[ChatPanel] Checking if notification ${id} is visible:`,
        notifications.some(n => n.id === id)
      );
    }, 500);

    return id;
  };

  return (
    <div className="flex flex-col h-full bg-white dark:bg-dark relative w-full">
      {/* Header cố định ở trên cùng với z-index cao */}
      <div className="sticky top-0 z-10 bg-white dark:bg-dark shadow-sm mb-4">
        <ChatHeader onNewChat={handleNewChat} onClose={onClose} />
      </div>

      {/* Uncomment to test notifications */}
      {/* <TestNotification /> */}

      <ChatContent
        messages={messages}
        isLoading={isLoading}
        notifications={notifications}
        onRemoveNotification={removeNotification}
      />
      <ChatInput
        onSendMessage={handleSendMessage}
        onKeywordDetected={onKeywordDetected}
        showNotification={showNotification}
      />
    </div>
  );
};

export default ChatPanel;
